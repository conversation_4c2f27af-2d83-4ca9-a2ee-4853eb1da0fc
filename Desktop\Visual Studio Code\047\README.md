# نظام مزاد أرقام السيارات 🚗

نظام إلكتروني متطور لبيع أرقام السيارات عبر المزادات المباشرة، مطور باستخدام Python و Flask مع دعم كامل للغة العربية.

## 🌟 المميزات الرئيسية

### 🎨 التصميم والواجهة
- تصميم عصري باستخدام Bootstrap 5 مع دعم RTL للعربية
- واجهة مستخدم سهلة ومتجاوبة مع جميع الأجهزة
- ألوان وتدرجات جذابة مع تأثيرات بصرية متقدمة
- خط Cairo العربي الأنيق

### 👥 نظام المستخدمين
- **المدير**: تحكم كامل في النظام
- **الموظف**: إدارة المزادات والأرقام
- **المزايد**: المشاركة في المزادات

### ⏰ نظام المزادات
- مزادات مؤقتة مع عداد تنازلي
- تحديث فوري للأسعار والعروض
- إنهاء تلقائي للمزادات
- تحديد الفائز تلقائياً

### 📱 الإشعارات
- إشعارات واتساب للفائزين
- تنبيهات فورية للعروض الجديدة
- رسائل تأكيد للمعاملات

### 📊 التقارير والتصدير
- تصدير التقارير بصيغة Excel
- طباعة الفواتير بصيغة PDF
- إحصائيات شاملة للمبيعات

## 🛠️ التقنيات المستخدمة

### Backend
- **Python 3.13**
- **Flask** - إطار العمل الرئيسي
- **SQLAlchemy** - قاعدة البيانات
- **Flask-Login** - إدارة الجلسات
- **SQLite** - قاعدة البيانات

### Frontend
- **Bootstrap 5 RTL** - التصميم المتجاوب
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخطوط العربية
- **JavaScript** - التفاعل والديناميكية

### المكتبات الإضافية
- **Pillow** - معالجة الصور
- **openpyxl** - تصدير Excel
- **xhtml2pdf** - تصدير PDF
- **requests** - طلبات HTTP

## 🚀 التثبيت والتشغيل

### طريقة سريعة (للتطوير)

#### 1. تحميل المشروع
```bash
git clone [repository-url]
cd 047
```

#### 2. الإعداد التلقائي
```bash
python setup.py
```

#### 3. تشغيل التطبيق
```bash
python run.py
# أو
start.bat  # على Windows
```

#### 4. فتح المتصفح
انتقل إلى: `http://127.0.0.1:5000`

### طريقة يدوية

#### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 2. إعداد قاعدة البيانات
```bash
python -c "
from app import app
from models import db
with app.app_context():
    db.create_all()
"
```

#### 3. إنشاء بيانات تجريبية (اختياري)
```bash
python create_sample_data.py
```

#### 4. تشغيل التطبيق
```bash
python app.py
```

### للإنتاج

#### باستخدام Gunicorn
```bash
pip install -r requirements-prod.txt
bash start_production.sh
```

#### باستخدام Docker
```bash
docker-compose up -d
```

## 👤 بيانات الدخول الافتراضية

### المدير
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

### مزايد تجريبي
- **اسم المستخدم**: bidder1
- **كلمة المرور**: bidder123

## 📁 هيكل المشروع

```
047/
├── app.py                 # التطبيق الرئيسي
├── models.py              # نماذج قاعدة البيانات
├── config.py              # إعدادات التطبيق
├── requirements.txt       # المكتبات المطلوبة
├── templates/             # قوالب HTML
│   ├── base.html         # القالب الأساسي
│   ├── login.html        # صفحة تسجيل الدخول
│   ├── dashboard.html    # لوحة التحكم
│   ├── auction_list.html # قائمة المزادات
│   └── auction_detail.html # تفاصيل المزاد
├── static/               # الملفات الثابتة
│   ├── css/
│   │   └── style.css     # التنسيقات المخصصة
│   ├── js/               # ملفات JavaScript
│   └── uploads/          # صور الأرقام المرفوعة
└── auction.db            # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🎯 كيفية الاستخدام

### للمدير/الموظف:
1. تسجيل الدخول بحساب المدير
2. الانتقال إلى لوحة التحكم
3. إضافة أرقام سيارات جديدة
4. رفع صور للأرقام
5. بدء المزادات
6. مراقبة العروض
7. تصدير التقارير

### للمزايدين:
1. تسجيل الدخول بحساب المزايد
2. تصفح المزادات النشطة
3. عرض تفاصيل الرقم المطلوب
4. تقديم عروض
5. متابعة حالة المزاد
6. استلام إشعار الفوز

## ⚙️ الإعدادات

### إعدادات قاعدة البيانات
يمكن تغيير نوع قاعدة البيانات في `config.py`:
```python
SQLALCHEMY_DATABASE_URI = 'sqlite:///auction.db'  # SQLite
# أو
SQLALCHEMY_DATABASE_URI = 'mysql://user:pass@localhost/auction'  # MySQL
```

### إعدادات واتساب
لتفعيل إشعارات واتساب، قم بتحديث `config.py`:
```python
WHATSAPP_API_URL = 'your-whatsapp-api-url'
WHATSAPP_API_TOKEN = 'your-api-token'
```

## 🔧 التطوير المستقبلي

### المميزات المخططة:
- [ ] ربط بوابات الدفع الإلكتروني (مدى، كي نت، فيزا)
- [ ] تطبيق جوال (Android/iOS) باستخدام Flutter
- [ ] بث مباشر للمزادات النهائية
- [ ] نظام تقييم المزايدين
- [ ] إشعارات البريد الإلكتروني
- [ ] نظام الرسائل الداخلية
- [ ] تقارير متقدمة وتحليلات
- [ ] نسخ احتياطية تلقائية

### التحسينات التقنية:
- [ ] استخدام Redis للتخزين المؤقت
- [ ] تحسين الأداء مع Gunicorn
- [ ] إضافة اختبارات وحدة
- [ ] نظام مراقبة الأخطاء
- [ ] تحسين الأمان

## 🛡️ الأمان

- تشفير كلمات المرور باستخدام Werkzeug
- حماية من CSRF باستخدام Flask-WTF
- تحديد أحجام الملفات المرفوعة
- تنظيف أسماء الملفات المرفوعة
- جلسات آمنة مع انتهاء صلاحية

## 📞 الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- إنشاء Issue في GitHub
- التواصل مع فريق التطوير

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

---

**تم التطوير بـ ❤️ للمجتمع العربي**
