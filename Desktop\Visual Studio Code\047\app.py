from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, send_file
from flask_login import <PERSON><PERSON><PERSON><PERSON><PERSON>, login_user, logout_user, login_required, current_user
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
from werkzeug.utils import secure_filename
from datetime import datetime, timedelta
import os
import json
from PIL import Image
import openpyxl
from io import BytesIO
import requests

from config import Config
from models import db, User, Plate, Bid, Settings

app = Flask(__name__)
app.config.from_object(Config)

# Initialize extensions
db.init_app(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة'
login_manager.login_message_category = 'info'

socketio = SocketIO(app, cors_allowed_origins="*")

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

def create_upload_folder():
    upload_folder = os.path.join(app.static_folder, 'uploads')
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)
    return upload_folder

@app.route('/')
def index():
    if current_user.is_authenticated:
        if current_user.is_employee():
            return redirect(url_for('dashboard'))
        else:
            return redirect(url_for('auction_list'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            login_user(user, remember=True)
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    if not current_user.is_employee():
        flash('غير مصرح لك بالوصول إلى لوحة التحكم', 'error')
        return redirect(url_for('auction_list'))
    
    plates = Plate.query.all()
    users = User.query.all()
    total_sales = db.session.query(db.func.sum(Plate.current_price)).filter(Plate.status == 'sold').scalar() or 0
    active_auctions = Plate.query.filter_by(status='active').count()
    
    return render_template('dashboard.html', 
                         plates=plates, 
                         users=users, 
                         total_sales=total_sales,
                         active_auctions=active_auctions)

@app.route('/auction')
@login_required
def auction_list():
    active_plates = Plate.query.filter_by(status='active').all()
    upcoming_plates = Plate.query.filter_by(status='pending').all()
    return render_template('auction_list.html', 
                         active_plates=active_plates, 
                         upcoming_plates=upcoming_plates)

@app.route('/auction/<int:plate_id>')
@login_required
def auction_detail(plate_id):
    plate = Plate.query.get_or_404(plate_id)
    bids = Bid.query.filter_by(plate_id=plate_id).order_by(Bid.timestamp.desc()).all()
    return render_template('auction_detail.html', plate=plate, bids=bids)

@app.route('/place_bid', methods=['POST'])
@login_required
def place_bid():
    data = request.get_json()
    plate_id = data.get('plate_id')
    amount = float(data.get('amount'))
    
    plate = Plate.query.get_or_404(plate_id)
    
    if not plate.is_active:
        return jsonify({'success': False, 'message': 'المزاد غير نشط'})
    
    if amount <= plate.current_price:
        return jsonify({'success': False, 'message': 'يجب أن يكون المبلغ أكبر من السعر الحالي'})
    
    # Create new bid
    bid = Bid(plate_id=plate_id, user_id=current_user.id, amount=amount)
    
    # Update all previous bids to not winning
    Bid.query.filter_by(plate_id=plate_id).update({'is_winning': False})
    bid.is_winning = True
    
    # Update plate current price
    plate.current_price = amount
    
    db.session.add(bid)
    db.session.commit()
    
    # Emit real-time update
    socketio.emit('new_bid', {
        'plate_id': plate_id,
        'amount': amount,
        'bidder': current_user.username,
        'time_remaining': plate.time_remaining
    }, room=f'auction_{plate_id}')
    
    return jsonify({'success': True, 'message': 'تم تسجيل العرض بنجاح'})

# Socket.IO events
@socketio.on('join_auction')
def on_join_auction(data):
    plate_id = data['plate_id']
    join_room(f'auction_{plate_id}')
    emit('status', {'msg': f'انضممت إلى مزاد الرقم {plate_id}'})

@socketio.on('leave_auction')
def on_leave_auction(data):
    plate_id = data['plate_id']
    leave_room(f'auction_{plate_id}')

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        
        # Create default admin user if not exists
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(username='admin', email='<EMAIL>', role='manager')
            admin.set_password('admin123')
            db.session.add(admin)
            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / admin123")
    
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
