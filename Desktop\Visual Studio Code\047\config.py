import os
from datetime import timedelta

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///auction.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Upload settings
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif'}
    
    # Session settings
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    
    # WhatsApp API settings (you'll need to configure these)
    WHATSAPP_API_URL = os.environ.get('WHATSAPP_API_URL') or ''
    WHATSAPP_API_TOKEN = os.environ.get('WHATSAPP_API_TOKEN') or ''
    
    # Default auction settings
    DEFAULT_AUCTION_DURATION = 300  # 5 minutes in seconds
    BID_INCREMENT = 100  # Minimum bid increment
